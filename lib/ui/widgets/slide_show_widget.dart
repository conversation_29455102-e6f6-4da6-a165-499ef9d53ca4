import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:signage/core/models/schedule_item.dart';
import 'package:signage/core/services/campaign_controller.dart';
import 'package:signage/core/controllers/api_data_controller.dart';
import 'package:signage/ui/widgets/playlist_widget.dart';
import 'package:signage/ui/widgets/textt_widget.dart';
import 'package:signage/ui/widgets/clock_widget.dart';
import 'package:signage/ui/widgets/shape_widget.dart';
import 'package:signage/ui/widgets/api_widget.dart';

/// A widget for displaying slide show content
class SlideShowWidget extends StatefulWidget {
  /// The schedule item containing slide data
  final ScheduleItem scheduleItem;

  /// The campaign controller
  final CampaignController campaignController;

  /// The width of the widget
  final double width;

  /// The height of the widget
  final double height;

  /// Callback when all content in the slide has finished playing
  final VoidCallback onComplete;

  /// Creates a SlideShowWidget
  const SlideShowWidget({
    super.key,
    required this.scheduleItem,
    required this.campaignController,
    required this.width,
    required this.height,
    required this.onComplete,
  });

  @override
  State<SlideShowWidget> createState() => _SlideShowWidgetState();
}

class _SlideShowWidgetState extends State<SlideShowWidget> {
  bool _isInitialized = false;
  List<Widget> _contentWidgets = [];

  // API data controller for managing API widgets
  ApiDataController? _apiDataController;

  @override
  void initState() {
    super.initState();
    debugPrint('SlideShowWidget: initState for ${widget.scheduleItem.name}');

    // Initialize the slide
    _initializeSlide();
  }

  Future<void> _initializeSlide() async {
    try {
      // Parse the content
      final content = widget.scheduleItem.content;
      if (content == null) {
        debugPrint('SlideShowWidget: no content, completing immediately');
        widget.onComplete();
        return;
      }

      // Parse the content if it's a string
      final dynamic contentData = content is String
          ? jsonDecode(content)
          : content;

      if (contentData is! List || contentData.isEmpty) {
        debugPrint('SlideShowWidget: invalid content format, completing immediately');
        widget.onComplete();
        return;
      }

      // Build the content widgets
      _contentWidgets = await _buildSlideContent(contentData);

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        // Start the API data controller timer after all widgets are initialized
        _startApiControllerTimer();
      }
    } catch (e) {
      debugPrint('SlideShowWidget: error initializing slide: $e');
      widget.onComplete();
    }
  }

  /// Start the API data controller timer after all widgets are initialized
  void _startApiControllerTimer() {
    // Check if we have an API data controller
    if (_apiDataController != null) {
      // Wait a short delay to ensure all APIWidgets have registered their videos
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && _apiDataController != null) {
          // Check if all APIWidgets are ready
          if (_apiDataController!.areAllWidgetsReady()) {
            // Start the global timer
            debugPrint('SlideShowWidget: All APIWidgets are ready, starting global timer');
            _apiDataController!.startGlobalTimer();
          } else {
            debugPrint('SlideShowWidget: Not all APIWidgets are ready yet, waiting...');

            // Try again after a short delay
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted && _apiDataController != null) {
                debugPrint('SlideShowWidget: Starting global timer regardless of widget state');
                _apiDataController!.startGlobalTimer();
              }
            });
          }
        }
      });
    }
  }

  Future<List<Widget>> _buildSlideContent(List<dynamic> contentData) async {
    final widgets = <Widget>[];

    // Get the design dimensions from the schedule item
    final designWidth = widget.scheduleItem.width?.toDouble() ?? widget.width;
    final designHeight = widget.scheduleItem.height?.toDouble() ?? widget.height;

    debugPrint('SlideShowWidget: Building content with design dimensions: $designWidth x $designHeight');
    debugPrint('SlideShowWidget: Actual screen dimensions: ${widget.width} x ${widget.height}');

    // Sort content items by zIndex (lower zIndex first, higher zIndex on top)
    final sortedContentData = List<dynamic>.from(contentData);
    sortedContentData.sort((a, b) {
      final aZIndex = (a['zIndex'] as num?)?.toInt() ?? 0;
      final bZIndex = (b['zIndex'] as num?)?.toInt() ?? 0;
      return aZIndex.compareTo(bZIndex);
    });

    for (final item in sortedContentData) {
      // Get common position and dimensions for all content types
      final double x = (item['x'] as num?)?.toDouble() ?? 0;
      final double y = (item['y'] as num?)?.toDouble() ?? 0;
      final double width = (item['width'] as num?)?.toDouble() ?? designWidth;
      final double height = (item['height'] as num?)?.toDouble() ?? designHeight;
      final String id = (item['id'] as String?) ?? '';
      final int zIndex = (item['zIndex'] as num?)?.toInt() ?? 0;

      // Handle playlist content type
      if (item['type'] == 'playlist' &&
          item['content'] != null &&
          item['content']['playlist'] != null) {

        debugPrint('SlideShowWidget: Original playlist position: ($x, $y), size: $width x $height, zIndex: $zIndex');

        // Create a playlist widget
        final playlist = item['content']['playlist'] as List;
        final playlistItems = playlist
            .map((item) => PlaylistItem.fromJson(item))
            .toList();

        if (playlistItems.isNotEmpty) {
          // Add the playlist widget with proper positioning
          // The scaling will be handled by _buildScaledWidgets
          widgets.add(
            Positioned(
              left: x,
              top: y,
              width: width,
              height: height,
              child: PlaylistWidget(
                key: ValueKey('playlist-$id'),
                id: widget.scheduleItem.id,
                playlistItems: playlistItems,
                campaignController: widget.campaignController,
                // Pass the original dimensions - scaling will be applied at render time
                width: width,
                height: height,
                onComplete: widget.onComplete,
              ),
            ),
          );

          // Log the expected scaled dimensions for debugging
          final scaledX = x * (widget.width / designWidth);
          final scaledY = y * (widget.height / designHeight);
          final scaledWidth = width * (widget.width / designWidth);
          final scaledHeight = height * (widget.height / designHeight);

          debugPrint('SlideShowWidget: Expected scaled position: ($scaledX, $scaledY), size: $scaledWidth x $scaledHeight');
        }
      }
      // Handle text content type
      else if (item['type'] == 'text' &&
               item['content'] != null) {

        debugPrint('SlideShowWidget: Original text position: ($x, $y), size: $width x $height, zIndex: $zIndex');

        // Get text content
        final textContent = item['content']['text'] as String? ?? '';

        // Add the text widget with proper positioning
        widgets.add(
          Positioned(
            left: x,
            top: y,
            width: width,
            height: height,
            child: TexttWidget(
              key: ValueKey('text-$id'),
              id: id,
              text: textContent,
              width: width,
              height: height,
              style: item['content'] as Map<String, dynamic>,
              animation: item['animation'] as Map<String, dynamic>?,
            ),
          ),
        );

        // Log the expected scaled dimensions for debugging
        final scaledX = x * (widget.width / designWidth);
        final scaledY = y * (widget.height / designHeight);
        final scaledWidth = width * (widget.width / designWidth);
        final scaledHeight = height * (widget.height / designHeight);

        debugPrint('SlideShowWidget: Expected scaled text position: ($scaledX, $scaledY), size: $scaledWidth x $scaledHeight');
      }
      // Handle date/clock content type
      else if (item['type'] == 'date' &&
               item['content'] != null) {

        debugPrint('SlideShowWidget: Original clock position: ($x, $y), size: $width x $height, zIndex: $zIndex');

        // Get locale and options
        final locale = item['content']['locale'] as String? ?? 'en-US';
        final options = item['content']['options'] as Map<String, dynamic>? ?? {};

        // Add the clock widget with proper positioning
        widgets.add(
          Positioned(
            left: x,
            top: y,
            width: width,
            height: height,
            child: ClockWidget(
              key: ValueKey('clock-$id'),
              id: id,
              width: width,
              height: height,
              style: item['content'] as Map<String, dynamic>,
              locale: locale,
              options: options,
              animation: item['animation'] as Map<String, dynamic>?,
            ),
          ),
        );

        // Log the expected scaled dimensions for debugging
        final scaledX = x * (widget.width / designWidth);
        final scaledY = y * (widget.height / designHeight);
        final scaledWidth = width * (widget.width / designWidth);
        final scaledHeight = height * (widget.height / designHeight);

        debugPrint('SlideShowWidget: Expected scaled clock position: ($scaledX, $scaledY), size: $scaledWidth x $scaledHeight');
      }
      // Handle shape content type
      else if (item['type'] == 'shape' &&
               item['content'] != null) {

        debugPrint('SlideShowWidget: Original shape position: ($x, $y), size: $width x $height, zIndex: $zIndex');

        // Add the shape widget with proper positioning
        widgets.add(
          Positioned(
            left: x,
            top: y,
            width: width,
            height: height,
            child: ShapeWidget(
              key: ValueKey('shape-$id'),
              id: id,
              width: width,
              height: height,
              content: item['content'] as Map<String, dynamic>,
              animation: item['animation'] as Map<String, dynamic>?,
            ),
          ),
        );

        // Log the expected scaled dimensions for debugging
        final scaledX = x * (widget.width / designWidth);
        final scaledY = y * (widget.height / designHeight);
        final scaledWidth = width * (widget.width / designWidth);
        final scaledHeight = height * (widget.height / designHeight);

        debugPrint('SlideShowWidget: Expected scaled shape position: ($scaledX, $scaledY), size: $scaledWidth x $scaledHeight');
      }
      // Handle API content type
      else if (item['type'] == 'api' &&
               item['content'] != null) {

        debugPrint('SlideShowWidget: Original API position: ($x, $y), size: $width x $height, zIndex: $zIndex');

        // Check if the scheduleItem has an API URL
        if (widget.scheduleItem.apiUrl != null && widget.scheduleItem.apiUrl!.isNotEmpty) {
          // Create the API data controller if it doesn't exist yet
          // This will be shared by all API widgets in this slide
          if (_apiDataController == null) {
            debugPrint('SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide');

            // Create a controller that will handle API data fetching and lifecycle
            _apiDataController = ApiDataController(
              apiUrl: widget.scheduleItem.apiUrl!,
              apiResponseData: null,
              apiDataPreviewDuration: widget.scheduleItem.apiDataPreviewDuration ?? 0,
              onComplete: widget.onComplete,
              totalRecords: 0,
            );

            debugPrint('SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: ${widget.scheduleItem.apiDataPreviewDuration ?? 0}');
          }

          // Create a separate API widget for each API node
          // Add the API widget with proper positioning
          widgets.add(
            Positioned(
              left: x,
              top: y,
              width: width,
              height: height,
              child: APIWidget(
                key: ValueKey('api-$id'),
                id: id,
                apiUrl: widget.scheduleItem.apiUrl!,
                width: width,
                height: height,
                content: item, // Pass the individual API node
                campaignController: widget.campaignController,
                apiDataPreviewDuration: widget.scheduleItem.apiDataPreviewDuration ?? 0,
                onComplete: widget.onComplete,
                scheduleItem: widget.scheduleItem,
                apiDataController: _apiDataController,
              ),
            ),
          );

          // Log the expected scaled dimensions for debugging
          final scaledX = x * (widget.width / designWidth);
          final scaledY = y * (widget.height / designHeight);
          final scaledWidth = width * (widget.width / designWidth);
          final scaledHeight = height * (widget.height / designHeight);

          debugPrint('SlideShowWidget: Expected scaled API position: ($scaledX, $scaledY), size: $scaledWidth x $scaledHeight');
        } else {
          debugPrint('SlideShowWidget: API URL is null or empty, skipping API widget');
        }
      }
      // Add support for other content types here as needed
    }

    // If no widgets were created, return an empty list
    if (widgets.isEmpty) {
      debugPrint('SlideShowWidget: no content widgets created, completing immediately');
      widget.onComplete();
    }

    return widgets;
  }

  @override
  void dispose() {
    debugPrint('SlideShowWidget: dispose for ${widget.scheduleItem.name}');

    // Dispose the API data controller if it exists
    if (_apiDataController != null) {
      debugPrint('SlideShowWidget: Disposing API data controller');
      _apiDataController!.dispose();
      _apiDataController = null;
    }

    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _contentWidgets.isEmpty) {
      // Return a transparent container while initializing
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.transparent,
      );
    }

    // Parse the background color
    Color backgroundColor = Colors.black;
    if (widget.scheduleItem.bgColor != null && widget.scheduleItem.bgColor!.isNotEmpty) {
      try {
        // Parse the color string (format: #RRGGBB or #AARRGGBB)
        final colorString = widget.scheduleItem.bgColor!.trim();
        if (colorString.startsWith('#')) {
          // Remove the # prefix
          final colorValue = colorString.substring(1);

          // Parse the color value
          if (colorValue.length == 6) {
            // Format: #RRGGBB
            backgroundColor = Color(int.parse('FF$colorValue', radix: 16));
          } else if (colorValue.length == 8) {
            // Format: #AARRGGBB
            backgroundColor = Color(int.parse(colorValue, radix: 16));
          }
        }
      } catch (e) {
        debugPrint('SlideShowWidget: error parsing background color: $e');
        backgroundColor = Colors.black; // Default to black on error
      }
    }

    // Get the design dimensions from the schedule item
    final designWidth = widget.scheduleItem.width?.toDouble() ?? widget.width;
    final designHeight = widget.scheduleItem.height?.toDouble() ?? widget.height;

    // Calculate scaling factors to stretch content to fill the screen
    final horizontalScale = widget.width / designWidth;
    final verticalScale = widget.height / designHeight;

    debugPrint('SlideShowWidget: Player screen size: ${widget.width}x${widget.height}');
    debugPrint('SlideShowWidget: Design size: $designWidth x $designHeight');
    debugPrint('SlideShowWidget: Scaling factors: horizontal=$horizontalScale, vertical=$verticalScale');

    return Container(
      key: ValueKey('slide-${widget.scheduleItem.id}'),
      width: widget.width,
      height: widget.height,
      color: backgroundColor,
      // Use a direct container with exact screen dimensions to ensure full coverage
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: Stack(
          fit: StackFit.expand, // Ensure stack fills the entire container
          children: _buildScaledWidgets(
            _contentWidgets,
            designWidth,
            designHeight,
            widget.width,
            widget.height
          ),
        ),
      ),
    );
  }

  /// Build scaled widgets based on the design dimensions and actual screen dimensions
  List<Widget> _buildScaledWidgets(
    List<Widget> widgets,
    double designWidth,
    double designHeight,
    double screenWidth,
    double screenHeight
  ) {
    final List<Widget> scaledWidgets = [];

    debugPrint('SlideShowWidget: Building scaled widgets with:');
    debugPrint('  - Design dimensions: $designWidth x $designHeight');
    debugPrint('  - Screen dimensions: $screenWidth x $screenHeight');
    debugPrint('  - Scale factors: ${screenWidth/designWidth} x ${screenHeight/designHeight}');

    for (final widget in widgets) {
      if (widget is Positioned) {
        // Calculate scaled position and dimensions
        final double? left = widget.left != null
            ? widget.left! * (screenWidth / designWidth)
            : null;
        final double? top = widget.top != null
            ? widget.top! * (screenHeight / designHeight)
            : null;
        final double? right = widget.right != null
            ? widget.right! * (screenWidth / designWidth)
            : null;
        final double? bottom = widget.bottom != null
            ? widget.bottom! * (screenHeight / designHeight)
            : null;
        final double? width = widget.width != null
            ? widget.width! * (screenWidth / designWidth)
            : null;
        final double? height = widget.height != null
            ? widget.height! * (screenHeight / designHeight)
            : null;

        debugPrint('SlideShowWidget: Scaling widget:');
        debugPrint('  - Original: left=${widget.left}, top=${widget.top}, width=${widget.width}, height=${widget.height}');
        debugPrint('  - Scaled: left=$left, top=$top, width=$width, height=$height');

        // Create a new positioned widget with scaled values
        scaledWidgets.add(
          Positioned(
            left: left,
            top: top,
            right: right,
            bottom: bottom,
            width: width,
            height: height,
            child: widget.child,
          ),
        );
      } else {
        // For non-positioned widgets, add them directly
        debugPrint('SlideShowWidget: Adding non-positioned widget directly');
        scaledWidgets.add(widget);
      }
    }

    return scaledWidgets;
  }
}
