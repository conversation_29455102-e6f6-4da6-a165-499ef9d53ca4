import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:video_player/video_player.dart';
import 'package:signage/ui/widgets/gauge_speedometer.dart';
import 'package:flutter_svg/flutter_svg.dart'; // For SVG support
import 'package:signage/core/services/campaign_controller.dart';
import 'package:signage/core/controllers/api_data_controller.dart';
import 'package:signage/ui/widgets/platform_video_widget.dart';

/// A widget for displaying API content in the SlideShowWidget
class APIWidget extends StatefulWidget {
  /// The unique ID for this API widget
  final String id;

  /// The API URL to fetch data from
  final String apiUrl;

  /// The width of the widget
  final double width;

  /// The height of the widget
  final double height;

  /// The content configuration (a single API node)
  final dynamic content;

  /// The campaign controller
  final CampaignController campaignController;

  /// The API data preview duration in seconds (0 means use default 10 seconds)
  final int apiDataPreviewDuration;

  /// Callback when all API content has finished playing
  final VoidCallback onComplete;

  /// The schedule item containing design dimensions
  final dynamic scheduleItem;

  /// The API data controller for managing lifecycle and data refresh
  final ApiDataController? apiDataController;

  /// Creates an APIWidget
  const APIWidget({
    super.key,
    required this.id,
    required this.apiUrl,
    required this.width,
    required this.height,
    required this.content,
    required this.campaignController,
    required this.apiDataPreviewDuration,
    required this.onComplete,
    this.scheduleItem,
    this.apiDataController,
  });

  @override
  State<APIWidget> createState() => _APIWidgetState();
}

class _APIWidgetState extends State<APIWidget> {
  bool _isInitialized = false;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // API response data
  dynamic _apiResponseData;

  // Current record index for multi-record responses
  int _currentRecordIndex = 0;

  // Total number of records in the API response
  int _totalRecords = 0;

  // Timer for advancing to the next record or completing
  Timer? _displayTimer;

  // List of widgets to display
  List<Widget> _contentWidgets = [];

  // Map to store video controllers by widget ID
  final Map<String, VideoPlayerController> _videoControllers = {};

  // Map to track if video has called onComplete
  final Map<String, bool> _videoCompletionFlags = {};

  @override
  void initState() {
    super.initState();
    debugPrint('APIWidget: initState for ${widget.id}');

    // If we have an API data controller, use it
    if (widget.apiDataController != null) {
      // Set up a listener to rebuild when the controller's state changes
      widget.apiDataController!.addListener(_onControllerUpdate);

      // If the controller is already initialized, use its data
      if (widget.apiDataController!.isInitialized) {
        _syncWithController();
      } else {
        // Otherwise, show loading state until the controller initializes
        setState(() {
          _isLoading = true;
        });
      }
    } else {
      // Fallback to direct API fetching if no controller is available
      // This should never happen in normal operation
      debugPrint('APIWidget: WARNING - No API data controller available, fetching data directly');
      _fetchApiData();
    }
  }

  /// Handle updates from the API data controller
  void _onControllerUpdate() {
    if (!mounted || widget.apiDataController == null) return;

    debugPrint('APIWidget: Controller update received for ${widget.id}');

    // Sync with the controller's state
    _syncWithController();
  }

  /// Sync this widget's state with the controller
  void _syncWithController() {
    if (!mounted || widget.apiDataController == null) return;

    final controller = widget.apiDataController!;
    bool needsRebuild = false;

    // Check if the record index has changed
    if (controller.isInitialized && _currentRecordIndex != controller.currentRecordIndex) {
      debugPrint('APIWidget: Syncing record index from ${_currentRecordIndex} to ${controller.currentRecordIndex}');

      // Clean up video controllers BEFORE changing the record index
      // This ensures videos from the previous record are properly disposed
      debugPrint('APIWidget: Cleaning up video controllers before changing record index');
      _cleanupVideoControllers();

      // Now it's safe to update the record index
      _currentRecordIndex = controller.currentRecordIndex;
      needsRebuild = true;
    }

    // Update our state based on the controller's state
    setState(() {
      _isLoading = controller.isLoading;
      _hasError = controller.hasError;
      _errorMessage = controller.errorMessage;

      if (controller.isInitialized) {
        _isInitialized = true;
        _apiResponseData = controller.apiResponseData;
        _totalRecords = controller.totalRecords;

        if (needsRebuild) {
          _contentWidgets = []; // Clear current widgets to avoid flicker
        }
      }
    });

    // If we're initialized and have a valid record index, rebuild content widgets
    if (_isInitialized && _currentRecordIndex < _totalRecords && (needsRebuild || _contentWidgets.isEmpty)) {
      _buildContentWidgets().then((_) {
        if (mounted) {
          setState(() {
            // Force a rebuild with the new content widgets
            debugPrint('APIWidget: Rebuilt content for record $_currentRecordIndex after sync');
          });
        }
      });
    }
  }

  @override
  void dispose() {
    debugPrint('APIWidget: dispose for ${widget.id}');

    // Remove controller listener if we have one
    if (widget.apiDataController != null) {
      widget.apiDataController!.removeListener(_onControllerUpdate);
    }

    // Clean up all resources
    _cleanupAllResources();

    super.dispose();
  }

  /// Cancel the active timer if any
  void _cancelTimer() {
    if (_displayTimer != null && _displayTimer!.isActive) {
      _displayTimer!.cancel();
      _displayTimer = null;
    }
  }

  /// Fetch data from the API endpoint
  Future<void> _fetchApiData() async {
    try {
      debugPrint('APIWidget: Fetching data from ${widget.apiUrl}');

      // Make the HTTP request
      final response = await http.get(Uri.parse(widget.apiUrl));

      if (response.statusCode == 200) {
        // Parse the response
        final dynamic data = jsonDecode(response.body);

        // Log the API response for debugging
        debugPrint('APIWidget: API Response: ${response.body}');

        if (data == null) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'API returned null data';
          });

          // Skip to next scheduleItem
          widget.onComplete();
          return;
        }

        // Store the API response data
        _apiResponseData = data;

        // Determine if it's a list or a single object
        if (data is List) {
          _totalRecords = data.length;
        } else {
          _totalRecords = 1;
        }

        debugPrint('APIWidget: Received $_totalRecords records from API');

        // Log the structure of the API response for debugging
        _logApiResponseStructure(data);

        // If we have an API data controller, update it with the fetched data
        if (widget.apiDataController != null) {
          // Update the controller's data using the proper API
          widget.apiDataController!.updateApiData(data, _totalRecords);

          // The controller will handle timing and state management
          // We'll be notified via the listener we set up in initState
        } else {
          // Build the content widgets for the first record
          await _buildContentWidgets();

          if (mounted) {
            setState(() {
              _isLoading = false;
              _isInitialized = true;
            });

            // This should never happen in normal operation, as all APIWidgets should have a controller
            debugPrint('APIWidget: WARNING - No API data controller available, starting local timer');
            _startDisplayTimer();
          }
        }
      } else {
        debugPrint('APIWidget: API request failed with status ${response.statusCode}');

        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'API request failed with status ${response.statusCode}';
          });

          // Skip to next scheduleItem
          widget.onComplete();
        }
      }
    } catch (e) {
      debugPrint('APIWidget: Error fetching API data: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Error fetching API data: $e';
        });

        // Skip to next scheduleItem
        widget.onComplete();
      }
    }
  }

  /// Build the content widgets based on the API response data
  Future<void> _buildContentWidgets() async {
    final widgets = <Widget>[];

    try {
      // Sync with controller if available
      if (widget.apiDataController != null && _isInitialized) {
        // Make sure we're using the controller's current record index
        _currentRecordIndex = widget.apiDataController!.currentRecordIndex;
      }

      // Get the current record data
      final dynamic recordData = _apiResponseData is List
          ? (_currentRecordIndex < (_apiResponseData as List).length
              ? _apiResponseData[_currentRecordIndex]
              : null)
          : _apiResponseData;

      if (recordData == null) {
        debugPrint('APIWidget: No data available for record index $_currentRecordIndex');
        _contentWidgets = widgets;
        return;
      }

      debugPrint('APIWidget: Building content widgets for record index $_currentRecordIndex');

      try {
        // Safely extract content configuration from the single API node
        final Map<String, dynamic> contentConfig = widget.content['content'] is Map
            ? widget.content['content'] as Map<String, dynamic>
            : {};

        // Log the content configuration for debugging
        debugPrint('APIWidget: Processing content item: ${contentConfig.toString()}');

        // Extract subtype, dataField, and placeholderUrl with null safety
        final String subtype = contentConfig['subtype'] is String ? contentConfig['subtype'] as String : '';
        final String dataField = contentConfig['dataField'] is String ? contentConfig['dataField'] as String : '';
        final String? placeholderUrl = contentConfig['placeholderUrl'] is String ? contentConfig['placeholderUrl'] as String : null;


        debugPrint('APIWidget: Content item details - subtype: $subtype, dataField: $dataField, placeholderUrl: $placeholderUrl');

        // Extract the data value using dot notation
        final dynamic dataValue = _extractDataValue(recordData, dataField);

        if (dataValue == null) {
          debugPrint('APIWidget: Data field "$dataField" not found in API response');
          return;
        }

        // Log the widget dimensions
        debugPrint('APIWidget: Widget dimensions - width: ${widget.width}, height: ${widget.height}');

        // Create the appropriate widget based on the subtype
        Widget? contentWidget;

        switch (subtype) {
          case 'api.image':
            contentWidget = await _buildImageWidget(dataValue, placeholderUrl, widget.width, widget.height);
            break;

          case 'api.text':
            contentWidget = _buildTextWidget(dataValue, contentConfig, widget.width, widget.height);
            break;

          case 'api.video':
            contentWidget = await _buildVideoWidget(dataValue, placeholderUrl, widget.width, widget.height);
            break;

          case 'api.gauge':
            contentWidget = _buildGaugeWidget(dataValue, contentConfig, widget.width, widget.height);
            break;

          default:
            debugPrint('APIWidget: Unknown subtype: $subtype');
        }

        if (contentWidget != null) {
          // Use the full width and height of the API widget for the content
          widgets.add(
            Positioned.fill(
              child: contentWidget,
            ),
          );
          debugPrint('APIWidget: Added $subtype widget with size ${widget.width}x${widget.height}');
        } else {
          debugPrint('APIWidget: Failed to create widget for subtype: $subtype');
        }
      } catch (itemError) {
        // Catch errors for individual items to prevent one bad item from breaking the entire widget
        debugPrint('APIWidget: Error processing content item: $itemError');
        debugPrint('APIWidget: Content item that caused the error: ${widget.content}');
      }
    } catch (e, stackTrace) {
      debugPrint('APIWidget: Error building content widgets: $e');
      debugPrint('APIWidget: Stack trace: $stackTrace');
    }

    // Update the content widgets
    _contentWidgets = widgets;
    debugPrint('APIWidget: Built ${widgets.length} content widgets');
  }

  /// Extract a value from the data using dot notation with enhanced support for arrays and nested structures
  dynamic _extractDataValue(dynamic data, String field) {
    if (data == null || field.isEmpty) {
      debugPrint('APIWidget: Cannot extract value - data is null or field is empty');
      return null;
    }

    // Split the field by dots
    final parts = field.split('.');
    debugPrint('APIWidget: Extracting field "$field" (parts: ${parts.join(', ')})');

    // Use a recursive deep search function to find the value
    final result = _deepSearch(data, parts, 0);

    if (result == null) {
      debugPrint('APIWidget: Field "$field" not found in data');
    } else {
      debugPrint('APIWidget: Found value for "$field": $result (${result.runtimeType})');
    }

    return result;
  }

  /// Recursively search for a value in a nested data structure
  dynamic _deepSearch(dynamic data, List<String> parts, int index) {
    // Base case: if we've processed all parts, return the current data
    if (index >= parts.length) {
      return data;
    }

    // Get the current part we're looking for
    final part = parts[index];

    // Handle different data types
    if (data is Map) {
      // Direct match in the map
      if (data.containsKey(part)) {
        return _deepSearch(data[part], parts, index + 1);
      }
    } else if (data is List) {
      // For lists, we have a few options:

      // 1. If the part is a numeric index, use it directly
      if (int.tryParse(part) != null) {
        final idx = int.parse(part);
        if (idx >= 0 && idx < data.length) {
          return _deepSearch(data[idx], parts, index + 1);
        }
      }

      // 2. If we're looking for a field in the first item of an array
      // This is useful for cases like "weather.0.icon" or just "weather.icon"
      // where "weather" is an array and we want the first item's icon
      if (data.isNotEmpty) {
        // Try the first item directly
        final firstResult = _deepSearch(data[0], parts.sublist(index), 0);
        if (firstResult != null) {
          return firstResult;
        }

        // If that didn't work, try searching through all items in the array
        for (final item in data) {
          final result = _deepSearch(item, parts.sublist(index), 0);
          if (result != null) {
            return result;
          }
        }
      }
    }

    // If we couldn't find a direct match, try a deep search in all properties
    if (data is Map) {
      // Look through all values in the map for a match
      for (final value in data.values) {
        // Skip null values and primitive types that can't contain our target
        if (value == null || value is num || value is String || value is bool) {
          continue;
        }

        final result = _deepSearch(value, parts.sublist(index), 0);
        if (result != null) {
          return result;
        }
      }
    }

    // If we get here, we couldn't find the value
    return null;
  }

  /// Build an image widget from API data
  Future<Widget?> _buildImageWidget(dynamic dataValue, String? placeholderUrl, double width, double height) async {
    String? imageUrl;

    // Log the input values for debugging
    debugPrint('APIWidget: Building image widget with dataValue: $dataValue (${dataValue.runtimeType}), placeholderUrl: $placeholderUrl');

    // Determine the image URL
    if (dataValue is String) {
      // If dataValue is a direct URL
      imageUrl = dataValue;
      debugPrint('APIWidget: dataValue is String: $imageUrl');
    } else if (dataValue is Map && dataValue.containsKey('url')) {
      // If dataValue has a url property
      imageUrl = dataValue['url'] as String?;
      debugPrint('APIWidget: dataValue is Map with url: $imageUrl');
    }

    // Check if the URL is valid (starts with http:// or https://)
    bool isValidUrl = false;
    if (imageUrl != null && imageUrl.isNotEmpty) {
      isValidUrl = imageUrl.startsWith('http://') || imageUrl.startsWith('https://');
      debugPrint('APIWidget: URL validation check: $imageUrl is ${isValidUrl ? 'valid' : 'invalid'}');
    }

    // If not a valid URL and placeholder exists, use placeholder with substitution
    if ((!isValidUrl) && placeholderUrl != null && placeholderUrl.isNotEmpty) {
      final String valueForSubstitution = dataValue?.toString() ?? '';

      // Check if the placeholder contains a field name pattern like {fieldname}
      if (placeholderUrl.contains('{') && placeholderUrl.contains('}')) {
        // Handle field name patterns like {weather.icon}
        final RegExp fieldPattern = RegExp(r'\{([^{}]+)\}');

        // Replace any field pattern with the actual value
        imageUrl = placeholderUrl.replaceAllMapped(fieldPattern, (match) {
          final String fieldName = match.group(1) ?? '';
          debugPrint('APIWidget: Found field pattern {$fieldName} in placeholder URL');
          // Replace the field pattern with the actual value
          return valueForSubstitution;
        });
      } else {
        // Simple {} replacement
        imageUrl = placeholderUrl.replaceAll('{}', valueForSubstitution);
      }

      debugPrint('APIWidget: Using placeholder URL with substitution: $placeholderUrl -> $imageUrl');
    }

    if (imageUrl == null || imageUrl.isEmpty) {
      debugPrint('APIWidget: No valid image URL could be determined');
      return null;
    }

    // Final URL validation check
    if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
      debugPrint('APIWidget: Final URL is not valid: $imageUrl');
      return Container(
        width: width,
        height: height,
        color: Colors.transparent,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.broken_image,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 8),
              const Text(
                'Invalid image URL',
                style: TextStyle(color: Colors.white),
              ),
              Text(
                imageUrl,
                style: const TextStyle(color: Colors.white70, fontSize: 12),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      );
    }

    debugPrint('APIWidget: Loading image from URL: $imageUrl');
    debugPrint('APIWidget: Using direct streaming from remote server for image');

    // Check if the image is an SVG
    final bool isSvg = imageUrl.toLowerCase().endsWith('.svg');
    debugPrint('APIWidget: Image type: ${isSvg ? 'SVG' : 'Regular image'}');
    debugPrint('APIWidget: Image dimensions: $width x $height');

    // Create a container with transparent background that fills the available space
    return Container(
      width: width, // Use original width - scaling is handled by parent
      height: height, // Use original height - scaling is handled by parent
      color: Colors.transparent, // Transparent background
      child: isSvg
        // Handle SVG images with fallback for animated SVGs
        ? _buildSvgWidget(imageUrl, width, height)
        // Handle regular images
        : Image.network(
            imageUrl,
            width: width, // Use original width - scaling is handled by parent
            height: height, // Use original height - scaling is handled by parent
            fit: BoxFit.fill, // Fill the entire space without preserving aspect ratio
            errorBuilder: (context, error, stackTrace) {
              debugPrint('APIWidget: Error loading image from $imageUrl: $error');
              // Show a simple container with transparent background on error
              return Container(
                width: width,
                height: height,
                color: Colors.transparent,
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.broken_image,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Failed to load image',
                        style: TextStyle(color: Colors.white),
                      ),
                      Text(
                        error.toString(),
                        style: const TextStyle(color: Colors.white70, fontSize: 12),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) {
                // Image is fully loaded
                return child;
              }
              // Show a transparent container while loading
              return Container(
                width: width,
                height: height,
                color: Colors.transparent,
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
    );
  }

  /// Build an SVG widget with fallback for animated SVGs
  Widget _buildSvgWidget(String url, double width, double height) {
    // For SVGs with animations, use a WebView approach
    // First try to detect if the URL is likely to contain animations
    if (url.contains('weather-icons')) {
      debugPrint('APIWidget: Using direct Image.network for likely animated SVG: $url');
      return _buildDirectImageWidget(url, width, height);
    }

    // Otherwise try SvgPicture first with fallback
    return SvgPicture.network(
      url,
      fit: BoxFit.fill, // Fill the entire space without preserving aspect ratio
      placeholderBuilder: (BuildContext context) => Container(
        width: width,
        height: height,
        color: Colors.transparent,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      ),
      // Handle SVG parsing errors (including animation elements)
      errorBuilder: (context, error, stackTrace) {
        debugPrint('APIWidget: Error loading SVG from $url: $error');

        // Check if the error is related to SVG animation elements
        final bool isAnimationError = error.toString().contains('animateTransform') ||
                                     error.toString().contains('animate');

        if (isAnimationError) {
          debugPrint('APIWidget: Detected SVG animation error, falling back to direct Image.network');
          return _buildDirectImageWidget(url, width, height);
        } else {
          // For other SVG errors, show error message
          return Container(
            width: width,
            height: height,
            color: Colors.transparent,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.broken_image,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Failed to load SVG',
                    style: TextStyle(color: Colors.white),
                  ),
                  Text(
                    error.toString(),
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  /// Build a direct image widget for animated SVGs
  Widget _buildDirectImageWidget(String url, double width, double height) {
    // Extract the weather code from the URL for weather icons
    String weatherCode = "01d"; // Default
    if (url.contains('weather-icons')) {
      final RegExp codeRegex = RegExp(r'([0-9]+[a-z]?)\.svg$');
      final Match? match = codeRegex.firstMatch(url);
      if (match != null && match.groupCount > 0) {
        weatherCode = match.group(1) ?? "01d";
      }
      debugPrint('APIWidget: Extracted weather code: $weatherCode');
    }

    // For SVGs, especially animated ones, we need to use a different approach
    // Convert the SVG URL to a PNG URL if it's from the weather icons
    if (url.contains('weather-icons') && url.endsWith('.svg')) {
      // Try to use Image.network first with the original SVG URL
      return Container(
        width: width,
        height: height,
        color: Colors.transparent,
        child: Image.network(
          url,
          width: width,
          height: height,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('APIWidget: SVG loading failed: $error, using icon fallback');

            // If SVG fails, use a Flutter icon with a colored background
            return Container(
              width: width,
              height: height,
              color: Colors.black12, // Semi-transparent background
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getWeatherIcon(weatherCode),
                      color: Colors.white,
                      size: width > height ? height / 3 : width / 3,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _getWeatherDescription(weatherCode),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    }

    // For other images, use the standard approach
    return Container(
      width: width,
      height: height,
      color: Colors.transparent,
      child: Image.network(
        url,
        width: width,
        height: height,
        fit: BoxFit.fill, // Fill the entire space without preserving aspect ratio
        errorBuilder: (context, imgError, imgStackTrace) {
          debugPrint('APIWidget: Direct image loading failed: $imgError');
          return Container(
            width: width,
            height: height,
            color: Colors.transparent,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.broken_image,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Failed to load image',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Get a weather icon based on the weather code
  IconData _getWeatherIcon(String code) {
    if (code.contains('01')) {
      return Icons.wb_sunny; // Clear sky
    } else if (code.contains('02')) {
      return Icons.cloud; // Few clouds (partly_cloudy_day not available in standard Icons)
    } else if (code.contains('03') || code.contains('04')) {
      return Icons.cloud; // Scattered or broken clouds
    } else if (code.contains('09')) {
      return Icons.grain; // Shower rain
    } else if (code.contains('10')) {
      return Icons.beach_access; // Rain
    } else if (code.contains('11')) {
      return Icons.flash_on; // Thunderstorm
    } else if (code.contains('13')) {
      return Icons.ac_unit; // Snow
    } else if (code.contains('50')) {
      return Icons.blur_on; // Mist
    } else {
      return Icons.cloud; // Default
    }
  }

  /// Get a weather description based on the weather code
  String _getWeatherDescription(String code) {
    if (code.contains('01')) {
      return 'Clear Sky';
    } else if (code.contains('02')) {
      return 'Few Clouds';
    } else if (code.contains('03')) {
      return 'Scattered Clouds';
    } else if (code.contains('04')) {
      return 'Broken Clouds';
    } else if (code.contains('09')) {
      return 'Shower Rain';
    } else if (code.contains('10')) {
      return 'Rain';
    } else if (code.contains('11')) {
      return 'Thunderstorm';
    } else if (code.contains('13')) {
      return 'Snow';
    } else if (code.contains('50')) {
      return 'Mist';
    } else {
      return 'Weather';
    }
  }

  /// Build a text widget from API data
  Widget _buildTextWidget(dynamic dataValue, Map<String, dynamic> contentConfig, double width, double height) {
    // Log the input values for debugging
    debugPrint('APIWidget: Building text widget with dataValue: $dataValue (${dataValue.runtimeType})');
    debugPrint('APIWidget: Content config: $contentConfig');

    // Convert data value to string
    final String text = dataValue.toString();
    debugPrint('APIWidget: Text content: "$text"');

    // Parse text style
    final Map<String, dynamic> style = contentConfig['style'] is Map ? contentConfig['style'] as Map<String, dynamic> : {};
    debugPrint('APIWidget: Text style: $style');

    // Parse text color
    Color textColor = Colors.white;
    if (style.containsKey('color')) {
      final dynamic colorValue = style['color'];
      if (colorValue is String) {
        textColor = _parseColor(colorValue);
        debugPrint('APIWidget: Text color from string: $colorValue -> $textColor');
      }
    }

    // Parse font size
    double fontSize = 16.0;
    if (style.containsKey('fontSize')) {
      final dynamic fontSizeValue = style['fontSize'];
      debugPrint('APIWidget: Raw fontSize value: $fontSizeValue (${fontSizeValue.runtimeType})');

      if (fontSizeValue is num) {
        fontSize = fontSizeValue.toDouble();
      } else if (fontSizeValue is String) {
        // Handle "120px" format by removing "px" and parsing
        final String cleanValue = fontSizeValue.replaceAll('px', '').trim();
        fontSize = double.tryParse(cleanValue) ?? 16.0;
      }
      debugPrint('APIWidget: Parsed font size: $fontSize');
    }

    // Get the design dimensions from the schedule item for font scaling only
    final double designWidth = widget.scheduleItem?.width?.toDouble() ?? 1920.0;
    final double designHeight = widget.scheduleItem?.height?.toDouble() ?? 1080.0;

    // Calculate scale factor for font size only
    final double horizontalScale = widget.width / designWidth;
    final double verticalScale = widget.height / designHeight;
    final double scaleFactor = horizontalScale < verticalScale ? horizontalScale : verticalScale;

    // Apply scaling only to font size
    fontSize = fontSize * scaleFactor;
    debugPrint('APIWidget: Scaled font size: $fontSize (scale factor: $scaleFactor)');

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (style.containsKey('fontWeight')) {
      final String weight = style['fontWeight'] is String ? style['fontWeight'] as String : 'normal';
      fontWeight = _parseFontWeight(weight);
      debugPrint('APIWidget: Font weight: $weight -> $fontWeight');
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.center;
    if (style.containsKey('textAlign')) {
      final String align = style['textAlign'] is String ? style['textAlign'] as String : 'center';
      textAlign = _parseTextAlign(align);
      debugPrint('APIWidget: Text alignment: $align -> $textAlign');
    }

    // Parse background color
    Color? backgroundColor;
    if (style.containsKey('backgroundColor') && style['backgroundColor'] is String) {
      final String bgColorStr = style['backgroundColor'] as String;
      if (bgColorStr != 'transparent') {
        backgroundColor = _parseColor(bgColorStr);
        debugPrint('APIWidget: Background color: $bgColorStr -> $backgroundColor');
      }
    }

    debugPrint('APIWidget: Creating text widget with content: "$text" at size $width x $height');

    // Create a container with proper styling that fills its allocated space
    return Container(
      width: width, // Use original width - scaling is handled by parent
      height: height, // Use original height - scaling is handled by parent
      color: backgroundColor ?? Colors.transparent, // Use transparent background
      alignment: Alignment.center, // Center the text
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: fontSize, // Only the font size is scaled
              fontWeight: fontWeight,
              // Add shadow for better visibility
              shadows: const [
                Shadow(
                  blurRadius: 3.0,
                  color: Color.fromRGBO(0, 0, 0, 0.5),
                  offset: Offset(1.0, 1.0),
                ),
              ],
            ),
            textAlign: textAlign,
            overflow: TextOverflow.visible,
          ),
        ),
      ),
    );
  }

  /// Build a video widget from API data
  Future<Widget?> _buildVideoWidget(dynamic dataValue, String? placeholderUrl, double width, double height) async {
    String? videoUrl;

    // Log the input values for debugging
    debugPrint('APIWidget: Building video widget with dataValue: $dataValue (${dataValue.runtimeType}), placeholderUrl: $placeholderUrl');

    // Determine the video URL
    if (dataValue is String) {
      // If dataValue is a direct URL
      videoUrl = dataValue;
      debugPrint('APIWidget: dataValue is String: $videoUrl');
    } else if (dataValue is Map && dataValue.containsKey('url')) {
      // If dataValue has a url property
      videoUrl = dataValue['url'] as String?;
      debugPrint('APIWidget: dataValue is Map with url: $videoUrl');
    }

    // Check if the URL is valid (starts with http:// or https://)
    bool isValidUrl = false;
    if (videoUrl != null && videoUrl.isNotEmpty) {
      isValidUrl = videoUrl.startsWith('http://') || videoUrl.startsWith('https://');
      debugPrint('APIWidget: URL validation check: $videoUrl is ${isValidUrl ? 'valid' : 'invalid'}');
    }

    // If not a valid URL and placeholder exists, use placeholder with substitution
    if ((!isValidUrl) && placeholderUrl != null && placeholderUrl.isNotEmpty) {
      final String valueForSubstitution = dataValue?.toString() ?? '';

      // Check if the placeholder contains a field name pattern like {fieldname}
      if (placeholderUrl.contains('{') && placeholderUrl.contains('}')) {
        // Handle field name patterns like {weather.icon}
        final RegExp fieldPattern = RegExp(r'\{([^{}]+)\}');

        // Replace any field pattern with the actual value
        videoUrl = placeholderUrl.replaceAllMapped(fieldPattern, (match) {
          final String fieldName = match.group(1) ?? '';
          debugPrint('APIWidget: Found field pattern {$fieldName} in placeholder URL');
          // Replace the field pattern with the actual value
          return valueForSubstitution;
        });
      } else {
        // Simple {} replacement
        videoUrl = placeholderUrl.replaceAll('{}', valueForSubstitution);
      }

      debugPrint('APIWidget: Using placeholder URL with substitution: $placeholderUrl -> $videoUrl');
    }

    if (videoUrl == null || videoUrl.isEmpty) {
      debugPrint('APIWidget: No valid video URL could be determined');
      return null;
    }

    // Final URL validation check
    if (!videoUrl.startsWith('http://') && !videoUrl.startsWith('https://')) {
      debugPrint('APIWidget: Final URL is not valid: $videoUrl');
      return Container(
        width: width,
        height: height,
        color: Colors.transparent,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.videocam_off,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 8),
              const Text(
                'Invalid video URL',
                style: TextStyle(color: Colors.white),
              ),
              Text(
                videoUrl,
                style: const TextStyle(color: Colors.white70, fontSize: 12),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      );
    }

    debugPrint('APIWidget: Loading video from URL: $videoUrl');
    debugPrint('APIWidget: Using platform-specific video widget for streaming');

    // Generate a unique ID for this video
    final String videoId = '${widget.id}-video-$_currentRecordIndex-${DateTime.now().millisecondsSinceEpoch}';

    // Register the video with the API data controller if available
    if (widget.apiDataController != null) {
      debugPrint('APIWidget: Registering video $videoId with ApiDataController');
      widget.apiDataController!.registerVideoWidget(videoId);
    } else {
      debugPrint('APIWidget: WARNING - No ApiDataController available to register video $videoId');
    }

    // Set looping based on apiDataPreviewDuration
    final bool shouldLoop = widget.apiDataPreviewDuration > 0;

    debugPrint('APIWidget: Using ${widget.apiDataPreviewDuration > 0 ? "fixed duration of ${widget.apiDataPreviewDuration}s" : "natural video duration"}');

    // Return the platform-specific video widget
    return PlatformVideoWidget(
      networkUrl: videoUrl,
      width: width,
      height: height,
      loop: shouldLoop,
      onInitialized: () {
        debugPrint('APIWidget: Video $videoId initialized');
      },
      onEnded: () {
        debugPrint('APIWidget: Video $videoId completed');

        // Mark this video as having called onComplete
        _videoCompletionFlags[videoId] = true;

        // Notify the API data controller if available
        if (widget.apiDataController != null) {
          debugPrint('APIWidget: Notifying ApiDataController that video $videoId has completed');
          widget.apiDataController!.notifyVideoComplete(videoId);
        }
        // Otherwise use the local logic
        else if (widget.apiDataPreviewDuration == 0 && mounted) {
          debugPrint('APIWidget: No ApiDataController available, advancing to next record locally');
          _advanceToNextRecord();
        }
      },
      onError: (error) {
        debugPrint('APIWidget: Video $videoId error: $error');

        // Clean up the video tracking
        _videoCompletionFlags.remove(videoId);

        // Unregister from controller if available
        if (widget.apiDataController != null) {
          widget.apiDataController!.unregisterVideoWidget(videoId);
        }

        // Advance to next record on error
        if (mounted) {
          _advanceToNextRecord();
        }
      },
    );
  }

/// Build a text widget from API data
  Widget _buildGaugeWidget(dynamic dataValue, Map<String, dynamic> contentConfig, double width, double height) {
    // Log the input values for debugging
    debugPrint('APIWidget: Building text widget with dataValue: $dataValue (${dataValue.runtimeType})');
    debugPrint('APIWidget: Content config: $contentConfig');

    // Convert data value to double
    final double value = dataValue is num ? dataValue.toDouble() : 0.0;
    debugPrint('APIWidget: Gauge value: $value');

    // Get the minValue and maxValue from the contentConfig
    final double minValue = contentConfig['minValue'] is num ? contentConfig['minValue'].toDouble() : 0.0;
    final double maxValue = contentConfig['maxValue'] is num ? contentConfig['maxValue'].toDouble() : 100.0;
    debugPrint('APIWidget: Gauge min and max values: $minValue, $maxValue');

    // Get the segments from the contentConfig
    final int segments = contentConfig['segments'] is num ? contentConfig['segments'].toInt() : 4;
    debugPrint('APIWidget: Gauge segments: $segments');

    //Get the segmentColors from the contentConfig
    final List<Color> segmentColors = (contentConfig['segmentColors'] as List<dynamic>?)?.map((colorStr) => _parseColor(colorStr as String)).toList() ?? [Colors.blue, Colors.green, Colors.orange, Colors.red];
    debugPrint('APIWidget: Gauge segmentColors: $segmentColors');

    //Get the customSegmentLabels from the contentConfig
    final List<CustomSegmentLabel> customSegmentLabels = (contentConfig['customSegmentLabels'] as List<dynamic>?)?.map((labelMap) {
      return CustomSegmentLabel(
        text: labelMap['text'] as String,
        position: labelMap['position'] as String,
        color: labelMap['color'] as String,
        currentValueText: labelMap['currentValueText'] as String?,
      );
    }).toList() ?? [];
    debugPrint('APIWidget: Gauge customSegmentLabels: $customSegmentLabels');

    //Get the currentValueText from the contentConfig
    final String? currentValueText = contentConfig['currentValueText'] as String?;
    debugPrint('APIWidget: Gauge currentValueText: $currentValueText');

    //Get the valueLabel from the contentConfig
    final String? valueLabel = contentConfig['valueLabel'] as String?;
    debugPrint('APIWidget: Gauge valueLabel: $valueLabel');

    //Get the needleColor from the contentConfig
    final Color needleColor = contentConfig['needleColor'] is String ? _parseColor(contentConfig['needleColor'] as String) : Colors.blue;
    debugPrint('APIWidget: Gauge needleColor: $needleColor');

    // Get the ringWidth from the contentConfig
    final double ringWidth = contentConfig['ringWidth'] is num ? contentConfig['ringWidth'].toDouble() : 20.0;
    debugPrint('APIWidget: Gauge ringWidth: $ringWidth');

    // Get the textColor from the contentConfig
    final Color textColor = contentConfig['textColor'] is String ? _parseColor(contentConfig['textColor'] as String) : Colors.black;
    debugPrint('APIWidget: Gauge textColor: $textColor');

    //Get the labelFontSize from the contentConfig
    final double labelFontSize = contentConfig['labelFontSize'] is num ? contentConfig['labelFontSize'].toDouble() : 14.0;
    debugPrint('APIWidget: Gauge labelFontSize: $labelFontSize');

    //Get the valueTextFontSize from the contentConfig
    final double valueTextFontSize = contentConfig['valueTextFontSize'] is num ? contentConfig['valueTextFontSize'].toDouble() : 24.0;
    debugPrint('APIWidget: Gauge valueTextFontSize: $valueTextFontSize');

    //Get the valueTextFontWeight from the contentConfig
    final FontWeight valueTextFontWeight = contentConfig['valueTextFontWeight'] is String ? _parseFontWeight(contentConfig['valueTextFontWeight'] as String) : FontWeight.bold;
    debugPrint('APIWidget: Gauge valueTextFontWeight: $valueTextFontWeight');

    // Get the paddingHorizontal from the contentConfig
    final double paddingHorizontal = contentConfig['paddingHorizontal'] is num ? contentConfig['paddingHorizontal'].toDouble() : 15.0;
    debugPrint('APIWidget: Gauge paddingHorizontal: $paddingHorizontal');

    // Get the paddingVertical from the contentConfig
    final double paddingVertical = contentConfig['paddingVertical'] is num ? contentConfig['paddingVertical'].toDouble() : 15.0;
    debugPrint('APIWidget: Gauge paddingVertical: $paddingVertical');

    // Get the design dimensions from the schedule item for font scaling only
    // final double designWidth = widget.scheduleItem?.width?.toDouble() ?? 1920.0;
    // final double designHeight = widget.scheduleItem?.height?.toDouble() ?? 1080.0;

    // // Calculate scale factor for font size only
    // final double horizontalScale = widget.width / designWidth;
    // final double verticalScale = widget.height / designHeight;
    // final double scaleFactor = horizontalScale < verticalScale ? horizontalScale : verticalScale;

    // Create a container with proper styling that fills its allocated space
    return Container(
      width: width, // Use original width - scaling is handled by parent
      height: height, // Use original height - scaling is handled by parent
      color: Colors.transparent, // Use transparent background
      alignment: Alignment.center, // Center the text
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: GaugeChart(
          value: value,
          minValue: minValue,
          maxValue: maxValue,
          segments: segments,
          segmentColors: segmentColors,
          customSegmentLabels: customSegmentLabels,
          currentValueText: currentValueText,
          valueLabel: valueLabel,
          width: width,
          height: height,
          needleColor:needleColor,
          ringWidth: ringWidth,
          textColor: textColor,
          labelFontSize: labelFontSize,
          valueTextFontSize: valueTextFontSize,
          valueTextFontWeight: valueTextFontWeight,
        ),
        ),
      ),
    );
  }

  /// Start the display timer for the current record
  /// This method should only be used when there's no ApiDataController
  void _startDisplayTimer() {
    // Cancel any existing timer
    _cancelTimer();

    // Determine the display duration
    int durationInSeconds = 15; // Default duration (changed from 20 to 15 seconds)

    if (widget.apiDataPreviewDuration > 0) {
      durationInSeconds = widget.apiDataPreviewDuration;
    }

    debugPrint('APIWidget: WARNING - Starting LOCAL display timer for $durationInSeconds seconds (no controller)');

    // Start a new timer
    _displayTimer = Timer(Duration(seconds: durationInSeconds), () {
      _advanceToNextRecord();
    });
  }

  /// Advance to the next record or complete if all records have been displayed
  void _advanceToNextRecord() {
    if (!mounted) return;

    // If we have an API data controller, let it handle the advancement
    if (widget.apiDataController != null) {
      // Clean up video controllers for the current record
      _cleanupVideoControllers();

      // Let the controller handle the advancement
      // The controller will notify all listeners, and our _onControllerUpdate method will be called
      debugPrint('APIWidget: Letting controller handle advancement from record $_currentRecordIndex');
      widget.apiDataController!.advanceToNextRecord();

      // We don't need to do anything else here
      // The _onControllerUpdate method will be called when the controller notifies listeners
      // This ensures all APIWidgets advance together
      return;
    }

    // Local handling if no controller is available
    // Cancel any active timer
    _cancelTimer();

    // Clean up video controllers for the current record
    _cleanupVideoControllers();

    // Increment the record index
    final nextIndex = _currentRecordIndex + 1;

    if (nextIndex < _totalRecords) {
      // Move to the next record
      setState(() {
        _currentRecordIndex = nextIndex;
        _contentWidgets = []; // Clear current widgets to avoid flicker
      });

      // Build the content widgets for the next record
      _buildContentWidgets().then((_) {
        if (mounted) {
          setState(() {});

          // Start the display timer for the next record
          // Note: For videos, the timer is set in _buildVideoWidget
          if (_videoControllers.isEmpty) {
            _startDisplayTimer();
          }
        }
      });
    } else {
      // All records have been displayed, perform final cleanup
      debugPrint('APIWidget: All records displayed, performing final cleanup');

      // Ensure all resources are properly disposed
      _cleanupAllResources();

      // Complete and move to next scheduleItem
      debugPrint('APIWidget: Advancing to next scheduleItem');
      widget.onComplete();
    }
  }

  /// Clean up all resources before disposing the widget
  void _cleanupAllResources() {
    // Cancel any active timer
    _cancelTimer();

    // Clean up video controllers
    _cleanupVideoControllers();

    // Clear content widgets
    _contentWidgets = [];

    // Reset state
    _currentRecordIndex = 0;
    _apiResponseData = null;

    debugPrint('APIWidget: All resources cleaned up');
  }

  /// Clean up video controllers for the current record
  void _cleanupVideoControllers() {
    if (_videoControllers.isEmpty) {
      debugPrint('APIWidget: No video controllers to clean up');
      return;
    }

    debugPrint('APIWidget: Cleaning up ${_videoControllers.length} video controllers');

    // Dispose all video controllers
    for (final videoId in _videoControllers.keys.toList()) {
      final controller = _videoControllers[videoId]!;
      debugPrint('APIWidget: Disposing video controller for $videoId');

      // Unregister the video from the API data controller if available
      if (widget.apiDataController != null) {
        debugPrint('APIWidget: Unregistering video $videoId from ApiDataController');
        widget.apiDataController!.unregisterVideoWidget(videoId);
      } else {
        debugPrint('APIWidget: WARNING - No ApiDataController available to unregister video $videoId');
      }

      try {
        // First, ensure the video is paused to stop audio immediately
        controller.pause();

        // Set volume to 0 to ensure no audio leakage during disposal
        controller.setVolume(0);

        // Remove all listeners from the controller
        // This is safer than trying to remove a specific listener
        // as we might not have the exact reference anymore
        controller.removeListener(() {});

        // Dispose the controller
        controller.dispose();
        debugPrint('APIWidget: Video controller for $videoId disposed');
      } catch (e) {
        debugPrint('APIWidget: Error disposing video controller for $videoId: $e');
      } finally {
        // Always remove from our tracking maps, even if disposal failed
        _videoControllers.remove(videoId);
        _videoCompletionFlags.remove(videoId);
      }
    }

    // Verify all controllers have been removed
    if (_videoControllers.isNotEmpty) {
      debugPrint('APIWidget: WARNING - Some video controllers were not properly removed: ${_videoControllers.keys.join(', ')}');
      _videoControllers.clear();
      _videoCompletionFlags.clear();
    }

    debugPrint('APIWidget: All video controllers cleaned up');
  }

  /// Log the structure of the API response for debugging
  void _logApiResponseStructure(dynamic data, [String prefix = '']) {
    if (data == null) {
      debugPrint('${prefix}null');
      return;
    }

    if (data is Map) {
      debugPrint('$prefix Map with keys: ${data.keys.join(', ')}');

      // Log the first level of nested structures
      data.forEach((key, value) {
        final String valueType = value.runtimeType.toString();
        if (value is Map) {
          debugPrint('$prefix  $key: Map with keys: ${value.keys.join(', ')}');
        } else if (value is List) {
          debugPrint('$prefix  $key: List with ${value.length} items');
          if (value.isNotEmpty) {
            debugPrint('$prefix    First item type: ${value.first.runtimeType}');
          }
        } else {
          debugPrint('$prefix  $key: $valueType = $value');
        }
      });
    } else if (data is List) {
      debugPrint('$prefix List with ${data.length} items');

      if (data.isNotEmpty) {
        final firstItem = data.first;
        debugPrint('$prefix  First item type: ${firstItem.runtimeType}');

        if (firstItem is Map) {
          debugPrint('$prefix  First item keys: ${firstItem.keys.join(', ')}');
        }
      }
    } else {
      debugPrint('$prefix ${data.runtimeType}: $data');
    }
  }

  /// Parse a color string to a Color object
  Color _parseColor(String colorStr) {
    debugPrint('APIWidget: Parsing color: $colorStr');

    // Handle hex colors
    if (colorStr.startsWith('#')) {
      String hexColor = colorStr.replaceFirst('#', '');

      // Ensure we have a valid hex color
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor'; // Add alpha channel
      } else if (hexColor.length != 8) {
        debugPrint('APIWidget: Invalid hex color format: $colorStr, using black');
        return Colors.black; // Default to black for invalid colors
      }

      try {
        final color = Color(int.parse(hexColor, radix: 16));
        debugPrint('APIWidget: Parsed hex color: $colorStr -> $color');
        return color;
      } catch (e) {
        debugPrint('APIWidget: Error parsing hex color: $e, using black');
        return Colors.black; // Default to black on error
      }
    }

    // Handle rgba colors like "rgba(255, 255, 255, 1)"
    if (colorStr.startsWith('rgba(') && colorStr.endsWith(')')) {
      try {
        // Extract the rgba values
        final values = colorStr
            .substring(5, colorStr.length - 1)
            .split(',')
            .map((s) => s.trim())
            .toList();

        if (values.length == 4) {
          final r = int.parse(values[0]);
          final g = int.parse(values[1]);
          final b = int.parse(values[2]);
          final a = double.parse(values[3]);

          final color = Color.fromRGBO(r, g, b, a);
          debugPrint('APIWidget: Parsed rgba color: $colorStr -> $color');
          return color;
        }
      } catch (e) {
        debugPrint('APIWidget: Error parsing rgba color: $e, using white');
        return Colors.white; // Default to white for rgba parsing errors
      }
    }

    // Handle named colors
    switch (colorStr.toLowerCase()) {
      case 'white':
        return Colors.white;
      case 'black':
        return Colors.black;
      case 'red':
        return Colors.red;
      case 'green':
        return Colors.green;
      case 'blue':
        return Colors.blue;
      case 'yellow':
        return Colors.yellow;
      case 'transparent':
        return Colors.transparent;
      default:
        debugPrint('APIWidget: Unknown color format: $colorStr, using white');
        return Colors.white; // Default to white for unknown formats
    }
  }

  /// Parse a font weight string to a FontWeight object
  FontWeight _parseFontWeight(String weight) {
    switch (weight.toLowerCase()) {
      case 'bold':
        return FontWeight.bold;
      case 'normal':
        return FontWeight.normal;
      case 'light':
        return FontWeight.w300;
      default:
        return FontWeight.normal;
    }
  }

  /// Parse a text align string to a TextAlign object
  TextAlign _parseTextAlign(String align) {
    switch (align.toLowerCase()) {
      case 'left':
        return TextAlign.left;
      case 'right':
        return TextAlign.right;
      case 'center':
        return TextAlign.center;
      case 'justify':
        return TextAlign.justify;
      default:
        return TextAlign.center;
    }
  }

  /// Check if we need to sync with the controller's current record index
  /// This is called during build to ensure we're always in sync with the controller
  void _checkControllerSync() {
    if (widget.apiDataController != null && _isInitialized) {
      final controllerIndex = widget.apiDataController!.currentRecordIndex;

      // If the controller's index is different from ours, update our state
      if (controllerIndex != _currentRecordIndex) {
        debugPrint('APIWidget: Detected controller record change during build: $_currentRecordIndex -> $controllerIndex');

        // Schedule a sync with the controller after this build completes
        // This avoids rebuilding during build
        Future.microtask(() {
          if (mounted) {
            _syncWithController();
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we need to sync with the controller
    _checkControllerSync();

    debugPrint('APIWidget: Building widget with state - isLoading: $_isLoading, hasError: $_hasError, isInitialized: $_isInitialized, contentWidgets: ${_contentWidgets.length}');
    debugPrint('APIWidget: Dimensions - width: ${widget.width}, height: ${widget.height}');

    if (_isLoading) {
      // Show a transparent container while loading
      debugPrint('APIWidget: Showing loading state');
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.transparent,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      );
    }

    if (_hasError) {
      // Show an error message
      debugPrint('APIWidget: Showing error state: $_errorMessage');
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.transparent,
        child: Center(
          child: Text(
            'Error: $_errorMessage',
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    if (!_isInitialized || _contentWidgets.isEmpty) {
      // Show a transparent container if not initialized or no content
      debugPrint('APIWidget: No content to display - initialized: $_isInitialized, widgets: ${_contentWidgets.length}');
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.transparent,
        child: const Center(
          child: Text(
            'No content to display',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    // Show the API content
    debugPrint('APIWidget: Displaying ${_contentWidgets.length} content widgets');
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.transparent,
      child: Stack(
        fit: StackFit.expand, // Ensure stack fills the entire container
        clipBehavior: Clip.none, // Allow content to overflow if needed
        children: _contentWidgets,
      ),
    );
  }
}
